import type { CacheModuleOptions } from '@nestjs/cache-manager';
import process from 'node:process';

const DEFAULT_TTL_MINUTES = 30;
const DEFAULT_MAX_ITEMS = 1000;
const SECONDS_PER_MINUTE = 60;
const MILLISECONDS_PER_SECOND = 1000;
const MILLISECONDS_PER_MINUTE = SECONDS_PER_MINUTE * MILLISECONDS_PER_SECOND;

/**
 * Get cache configuration from environment variables
 * Defaults:
 * - CACHE_ENABLED: true
 * - CACHE_TTL_MINUTES: 30
 * - CACHE_MAX_ITEMS: 1000
 * - CACHE_STORE: memory
 */
export function getCacheConfig(): CacheModuleOptions {
  const cacheEnabledEnv = process.env.CACHE_ENABLED;
  const enabled = cacheEnabledEnv?.toLowerCase() !== 'false'; // Default to true

  const ttlMinutesEnv = process.env.CACHE_TTL_MINUTES;
  const ttlMinutes = Number.parseInt(
    ttlMinutesEnv ?? String(DEFAULT_TTL_MINUTES),
    10,
  );

  const maxItemsEnv = process.env.CACHE_MAX_ITEMS;
  const maxItems = Number.parseInt(
    maxItemsEnv ?? String(DEFAULT_MAX_ITEMS),
    10,
  );

  // const store = process.env.CACHE_STORE ?? 'memory'; // Reserved for future Redis support

  if (!enabled) {
    return {
      ttl: 0, // No caching
      max: 0,
      isGlobal: true,
    };
  }

  return {
    ttl: ttlMinutes * MILLISECONDS_PER_MINUTE, // Convert minutes to milliseconds
    max: maxItems,
    isGlobal: true,
    // Note: For Redis or other stores, additional configuration would be needed
    // Currently only supports in-memory store
  };
}

/**
 * Cache configuration for daemon response caching
 * Configurable via environment variables
 */
export const cacheConfig: CacheModuleOptions = getCacheConfig();

/**
 * Cache key prefixes for different data types
 */
export const CACHE_KEYS = {
  CRYPTO_STATISTICS: 'crypto:statistics',
  STOCK_STATISTICS: 'stock:statistics',
  CRYPTO_INDICATORS: 'crypto:indicators',
  STOCK_INDICATORS: 'stock:indicators',
} as const;

/**
 * Generate cache key for indicator requests
 */
export function generateIndicatorCacheKey(
  type: 'crypto' | 'stock',
  symbol: string,
  conversionCurrency: string,
): string {
  const prefix
    = type === 'crypto'
      ? CACHE_KEYS.CRYPTO_INDICATORS
      : CACHE_KEYS.STOCK_INDICATORS;
  return `${prefix}:${symbol}:${conversionCurrency}`;
}
