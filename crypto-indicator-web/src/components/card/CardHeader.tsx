import React from 'react';

import { CSS_CLASSES, CURRENCIES } from '@/constants/app';
import { formatters } from '@/utils/formatters';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated/index';

interface CryptoCardHeaderProps {
  crypto: CryptoCurrencyStatisticsDto | StockStatisticsDto;
  usdData: IndicatorValueDto | undefined;
  onSymbolClick: (e: React.MouseEvent) => void;
  assetType?: 'crypto' | 'stock';
}

export const CardHeader: React.FC<CryptoCardHeaderProps> = ({
  crypto,
  usdData,
  onSymbolClick,
  assetType = 'crypto',
}) => {
  return (
    <div className="crypto-card-header">
      <div className="crypto-card-symbol">
        <div className={CSS_CLASSES.SYMBOL_CELL}>
          <button
            className="clickable-symbol"
            onClick={onSymbolClick}
            type="button"
            title={assetType === 'crypto' ? `🔗 Click to view ${crypto.symbol} on CoinMarketCap (opens in new tab)` : `📈 Click to view ${crypto.symbol} on Yahoo Finance (opens in new tab)`}
            aria-label={assetType === 'crypto' ? `View ${crypto.symbol} on CoinMarketCap` : `View ${crypto.symbol} on Yahoo Finance`}
          >
            <strong>{crypto.symbol}</strong>
          </button>
        </div>
      </div>
      <div className="crypto-card-price">
        <span className="price-label">
          {assetType === 'crypto' ? 'USD' : 'Price'}
        </span>
        <span className="price-value">
          {formatters.price(
            usdData?.close,
            assetType === 'crypto' ? CURRENCIES.USD : crypto.conversionCurrency,
          )}
        </span>
      </div>
    </div>
  );
};
