import React from 'react';

import { SignalBadge } from '@/components/signals/SignalBadge';
import { CURRENCIES } from '@/constants/app';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated/index';

interface CryptoCardSignalsProps {
  symbol: string;
  usdData: IndicatorValueDto | undefined;
  btcData: IndicatorValueDto | undefined;
  usdTooltip: string;
  btcTooltip: string;
  onSignalClick: (symbol: string, currency: string) => void;
  assetType?: 'crypto' | 'stock';
  crypto: CryptoCurrencyStatisticsDto | StockStatisticsDto;
}

// Helper component for signal group wrapper
const SignalGroupWrapper: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => (
  <div
    onClick={e => {
      e.stopPropagation();
    }}
    onKeyDown={e => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        e.stopPropagation();
      }
    }}
    role="button"
    tabIndex={0}
  >
    {children}
  </div>
);

// Helper component for USD signal group
const UsdSignalGroup: React.FC<{
  assetType: 'crypto' | 'stock';
  usdData: IndicatorValueDto | undefined;
  symbol: string;
  crypto: CryptoCurrencyStatisticsDto | StockStatisticsDto;
  usdTooltip: string;
  onSignalClick: (symbol: string, currency: string) => void;
}> = ({ assetType, usdData, symbol, crypto, usdTooltip, onSignalClick }) => (
  <div className="signal-group">
    <span className="signal-label">
      {assetType === 'crypto' ? 'USD Signal' : 'Signal'}
    </span>
    <SignalGroupWrapper>
      <SignalBadge
        color={usdData?.color}
        onClick={() => {
          onSignalClick(
            symbol,
            assetType === 'crypto' ? CURRENCIES.USD : crypto.conversionCurrency,
          );
        }}
        clickable
        title={usdTooltip}
      />
    </SignalGroupWrapper>
  </div>
);

// Helper component for BTC signal group
const BtcSignalGroup: React.FC<{
  btcData: IndicatorValueDto | undefined;
  symbol: string;
  btcTooltip: string;
  onSignalClick: (symbol: string, currency: string) => void;
}> = ({ btcData, symbol, btcTooltip, onSignalClick }) => (
  <div className="signal-group">
    <span className="signal-label">BTC Signal</span>
    <SignalGroupWrapper>
      <SignalBadge
        color={btcData?.color}
        onClick={() => {
          onSignalClick(symbol, CURRENCIES.BTC);
        }}
        clickable
        title={btcTooltip}
      />
    </SignalGroupWrapper>
  </div>
);

export const CardSignals: React.FC<CryptoCardSignalsProps> = ({
  symbol,
  usdData,
  btcData,
  usdTooltip,
  btcTooltip,
  onSignalClick,
  assetType = 'crypto',
  crypto,
}) => {
  return (
    <div className="crypto-card-signals">
      <UsdSignalGroup
        assetType={assetType}
        usdData={usdData}
        symbol={symbol}
        crypto={crypto}
        usdTooltip={usdTooltip}
        onSignalClick={onSignalClick}
      />
      <BtcSignalGroup
        btcData={btcData}
        symbol={symbol}
        btcTooltip={btcTooltip}
        onSignalClick={onSignalClick}
      />
    </div>
  );
};
