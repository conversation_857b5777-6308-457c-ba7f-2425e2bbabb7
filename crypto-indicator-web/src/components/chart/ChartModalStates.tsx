import React from 'react';

import { ErrorBoundary } from '@/components/ErrorBoundary';
import { ErrorState } from '@/components/ui/ErrorState';
import { LoadingState } from '@/components/ui/LoadingState';
import { CSS_CLASSES, UI_TEXT } from '@/constants/app';

import { ChartModal } from './ChartModal';

import type {
  CryptoCurrencyStatisticsDto,
  StockStatisticsDto,
} from '@/generated/index';

interface ChartModalStatesProps {
  showChart: boolean;
  chartData: CryptoCurrencyStatisticsDto | StockStatisticsDto | null;
  chartLoading: boolean;
  chartError: string | null;
  onClose: () => void;
}

export const ChartModalStates: React.FC<ChartModalStatesProps> = ({
  showChart,
  chartData,
  chartLoading,
  chartError,
  onClose,
}) => {
  return (
    <>
      {showChart && chartData && (
        <ErrorBoundary>
          <ChartModal data={chartData} onClose={onClose} />
        </ErrorBoundary>
      )}

      {chartLoading && (
        <div className={CSS_CLASSES.CHART_MODAL}>
          <div className={CSS_CLASSES.CHART_MODAL_CONTENT}>
            <LoadingState message={UI_TEXT.LOADING_CHART_DATA} />
          </div>
        </div>
      )}

      {chartError !== null && chartError !== undefined && (
        <div className={CSS_CLASSES.CHART_MODAL}>
          <div className={CSS_CLASSES.CHART_MODAL_CONTENT}>
            <ErrorState message={chartError} onRetry={onClose} showRetry />
          </div>
        </div>
      )}
    </>
  );
};
