import React from 'react';

import type { FilterConfig, StockFilterConfig } from '@/types/table';

interface FilterDrawerContentProps {
  filterConfig: FilterConfig | StockFilterConfig;
  onUsdSignalChange: (
    signal: FilterConfig['usdSignal'] | StockFilterConfig['usdSignal'],
  ) => void;
  onBtcSignalChange: (signal: FilterConfig['btcSignal']) => void;
  assetType?: 'crypto' | 'stock';
}

export const FilterDrawerContent: React.FC<FilterDrawerContentProps> = ({
  filterConfig,
  onUsdSignalChange,
  onBtcSignalChange,
  assetType = 'crypto',
}) => {
  const signalLabel = assetType === 'crypto' ? 'USD Signal' : 'Signal';
  const signalValue =
    'usdSignal' in filterConfig ? filterConfig.usdSignal : 'all';

  return (
    <div className="filter-drawer-content">
      <div className="filter-section">
        <label htmlFor="mobile-signal">{signalLabel}</label>
        <select
          id="mobile-signal"
          value={signalValue}
          onChange={e => {
            const value = e.target.value as 'all' | 'gold' | 'blue' | 'gray';
            onUsdSignalChange(value);
          }}
          className="filter-drawer-select"
        >
          <option value="all">All Signals</option>
          <option value="gold">🟡 Gold (Bullish)</option>
          <option value="blue">🔵 Blue (Bearish)</option>
          <option value="gray">⚪ Gray (Neutral)</option>
        </select>
      </div>

      <div className="filter-section">
        <label htmlFor="mobile-btc-signal">BTC Signal</label>
        <select
          id="mobile-btc-signal"
          value={'btcSignal' in filterConfig ? filterConfig.btcSignal : 'all'}
          onChange={e => {
            const value = e.target.value as 'all' | 'gold' | 'blue' | 'gray';
            onBtcSignalChange(value);
          }}
          className="filter-drawer-select"
        >
          <option value="all">All Signals</option>
          <option value="gold">🟡 Gold (Bullish)</option>
          <option value="blue">🔵 Blue (Bearish)</option>
          <option value="gray">⚪ Gray (Neutral)</option>
        </select>
      </div>
    </div>
  );
};
