import React from "react";

interface FilterDrawerFooterProps {
  filteredCount: number;
  totalCount: number;
  hasActiveFilters: boolean;
  onClearFilters: () => void;
  onClose: () => void;
  assetType?: 'crypto' | 'stock';
}

export const FilterDrawerFooter: React.FC<FilterDrawerFooterProps> = ({
  filteredCount,
  totalCount,
  hasActiveFilters,
  onClearFilters,
  onClose,
  assetType = 'crypto',
}) => {
  return (
    <div className="filter-drawer-footer">
      <div className="filter-results-mobile">
        Showing {filteredCount} of {totalCount} {assetType === 'crypto' ? 'cryptocurrencies' : 'stocks'}
      </div>

      <div className="filter-drawer-actions">
        {hasActiveFilters && (
          <button onClick={onClearFilters} className="filter-drawer-clear">
            Clear All
          </button>
        )}
        <button onClick={onClose} className="filter-drawer-apply">
          Apply Filters
        </button>
      </div>
    </div>
  );
};
