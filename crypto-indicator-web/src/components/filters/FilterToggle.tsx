import React from 'react';

interface FilterToggleProps {
  onClick: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
}

export const FilterToggle: React.FC<FilterToggleProps> = ({
  onClick,
  hasActiveFilters,
  filteredCount,
  totalCount,
}) => {
  return (
    <div className="filter-toggle-container">
      <button
        className={`filter-toggle ${hasActiveFilters ? 'active' : ''}`}
        onClick={onClick}
        aria-label="Open filters"
      >
        <span className="filter-icon">⚙️</span>
        {hasActiveFilters && (
          <span className="filter-badge">
            {filteredCount}
          </span>
        )}
      </button>
      
      {hasActiveFilters && (
        <div className="filter-status">
          {filteredCount} of {totalCount}
        </div>
      )}
    </div>
  );
};
