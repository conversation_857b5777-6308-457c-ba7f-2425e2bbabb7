import React from 'react';

import { ResponsiveTableContainer } from '@/components/table/ResponsiveTableContainer';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated';
import type { StockFilterConfig, StockSortColumn } from '@/types/table';

// Dummy formatDate function for stocks (can be enhanced later)
const formatDate = (date?: string): string => {
  if (date === null || date === undefined || date === '') {
    return '';
  }
  return new Date(date).toLocaleDateString();
};

interface StockMainContentProps {
  data: StockStatisticsDto[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  filterConfig: StockFilterConfig;
  hasActiveFilters: boolean;
  onSignalClick: (symbol: string, conversionCurrency: string) => void;
  onRefresh: () => Promise<void>;
  onSort: (column: StockSortColumn) => void;
  getSortDirection: (column: StockSortColumn) => 'asc' | 'desc' | null;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: 'all' | 'gold' | 'blue' | 'gray') => void;
  onBtcSignalChange: (signal: 'all' | 'gold' | 'blue' | 'gray') => void;
  onClearFilters: () => void;
  btcStatistics: StockStatisticsDto[];
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

export const StockMainContent: React.FC<StockMainContentProps> = ({
  data,
  totalCount,
  filteredCount,
  loading,
  filterConfig,
  hasActiveFilters,
  onSignalClick,
  onRefresh,
  onSort,
  getSortDirection,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  btcStatistics,
  findBtcDataForSymbol,
}) => {
  return (
    <div className="stock-main-content">
      <ResponsiveTableContainer
        data={data}
        btcStatistics={btcStatistics}
        onSignalClick={onSignalClick}
        formatDate={formatDate}
        findBtcDataForSymbol={findBtcDataForSymbol} // Use the same BTC data function
        onSort={onSort}
        getSortDirection={getSortDirection}
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange} // Handle USD signal changes for stocks
        onBtcSignalChange={onBtcSignalChange} // Handle BTC signal changes for stocks
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
        assetType="stock" // Add this prop to distinguish between crypto and stock
        onRefresh={onRefresh}
        isLoading={loading}
      />

      {data.length === 0 && !loading && (
        <div className="no-data">
          <p>No stock data available.</p>
        </div>
      )}
    </div>
  );
};
