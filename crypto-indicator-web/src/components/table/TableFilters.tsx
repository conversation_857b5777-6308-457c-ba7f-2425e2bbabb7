import React from 'react';

import type { FilterConfig, StockFilterConfig } from '@/types/table';

// Component-specific styles
import '../../styles/components/filters.css';

interface TableFiltersProps {
  filterConfig: FilterConfig | StockFilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig['usdSignal'] | StockFilterConfig['usdSignal']) => void;
  onBtcSignalChange: (signal: FilterConfig['btcSignal']) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
  assetType?: 'crypto' | 'stock';
}

// eslint-disable-next-line max-lines-per-function
export const TableFilters: React.FC<TableFiltersProps> = ({
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
  assetType = 'crypto',
}) => {
  const assetName = assetType === 'crypto' ? 'cryptocurrencies' : 'stocks';
  const searchPlaceholder = `Search ${assetName}...`;
  const signalLabel = assetType === 'crypto' ? 'USD Signal:' : 'Signal:';

  return (
    <div className="table-filters">
      <div className="filters-row">
        <div className="filter-group">
          <label htmlFor="symbol-search">Search:</label>
          <input
            id="symbol-search"
            type="text"
            placeholder={searchPlaceholder}
            value={filterConfig.symbolSearch}
            onChange={(e) => { onSymbolSearchChange(e.target.value); }}
            className="filter-input"
          />
        </div>

        <div className="filter-group">
          <label htmlFor="signal">{signalLabel}</label>
          <select
            id="signal"
            value={'usdSignal' in filterConfig ? filterConfig.usdSignal : 'all'}
            onChange={(e) => {
              const value = e.target.value as 'all' | 'gold' | 'blue' | 'gray';
              onUsdSignalChange(value);
            }}
            className="filter-select"
          >
            <option value="all">All</option>
            <option value="gold">Gold (Bullish)</option>
            <option value="blue">Blue (Bearish)</option>
            <option value="gray">Gray (Neutral)</option>
          </select>
        </div>

        {/* BTC Signal filter for both crypto and stock - user wants identical table structure */}
        <div className="filter-group">
          <label htmlFor="btc-signal">BTC Signal:</label>
          <select
            id="btc-signal"
            value={'btcSignal' in filterConfig ? filterConfig.btcSignal : 'all'}
            onChange={(e) => { onBtcSignalChange(e.target.value as FilterConfig['btcSignal']); }}
            className="filter-select"
          >
            <option value="all">All</option>
            <option value="gold">Gold (Bullish)</option>
            <option value="blue">Blue (Bearish)</option>
            <option value="gray">Gray (Neutral)</option>
          </select>
        </div>
        <div className="filter-actions">
          {hasActiveFilters && (
            <button
              onClick={onClearFilters}
              className="clear-filters-btn"
              type="button"
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>

      <div className="filter-results">
        <span className="results-count">
          Showing {filteredCount} of {totalCount} {assetName}
          {hasActiveFilters && <span className="filtered-indicator"> (filtered)</span>}
        </span>
      </div>
    </div>
  );
};
