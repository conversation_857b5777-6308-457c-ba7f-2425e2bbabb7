import React from 'react';

import type { TabNavigationProps } from '@/types/tabs';

// Component-specific styles
import '../../styles/components/tab-navigation.css';

export const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  onTabChange,
  tabs,
}) => {
  return (
    <div className="tab-navigation">
      <div className="tab-list" role="tablist">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            role="tab"
            aria-selected={activeTab === tab.id}
            aria-controls={`${tab.id}-panel`}
            id={`${tab.id}-tab`}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => { onTabChange(tab.id); }}
          >
            {tab.icon !== undefined && tab.icon !== '' && <span className="tab-icon">{tab.icon}</span>}
            <span className="tab-label">{tab.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};
