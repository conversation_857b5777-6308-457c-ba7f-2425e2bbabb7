import React from 'react';

import { CSS_CLASSES,UI_TEXT } from '@/constants/app';

interface TableControlsProps {
  totalCount: number;
  loading: boolean;
  onRefresh: () => void;
}

export const TableControls: React.FC<TableControlsProps> = ({
  totalCount,
  loading,
  onRefresh,
}) => {
  return (
    <div className={CSS_CLASSES.STATS_INFO}>
      <div>
        <strong>{totalCount}</strong> {UI_TEXT.CRYPTOCURRENCIES}
      </div>
      <button onClick={onRefresh} disabled={loading}>
        {loading ? UI_TEXT.REFRESHING : UI_TEXT.REFRESH}
      </button>
    </div>
  );
};
