import { useCallback,useState } from 'react';

import { useApiClient } from './useApiClient';

import type { CryptoCurrencyStatisticsDto } from '@/generated';

interface UseChartDataReturn {
  chartData: CryptoCurrencyStatisticsDto | null;
  chartLoading: boolean;
  chartError: string | null;
  fetchChartData: (symbol: string, conversionCurrency: string) => Promise<void>;
  clearChartData: () => void;
}

/**
 * Hook for managing chart-specific data fetching
 * Handles indicator data for individual cryptocurrencies
 */
export const useChartData = (): UseChartDataReturn => {
  const [chartData, setChartData] = useState<CryptoCurrencyStatisticsDto | null>(null);
  const [chartLoading, setChartLoading] = useState(false);
  const [chartError, setChartError] = useState<string | null>(null);
  const client = useApiClient();

  const fetchChartData = useCallback(async (symbol: string, conversionCurrency: string) => {
    setChartLoading(true);
    setChartError(null);
    
    try {
      const data = await client.CryptoStatisticsController_getCryptoIndicators(symbol, conversionCurrency);
      setChartData(data);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch chart data';
      setChartError(errorMessage);
      // eslint-disable-next-line no-console
      console.error('Error fetching chart data:', error);
    } finally {
      setChartLoading(false);
    }
  }, [client]);

  const clearChartData = useCallback(() => {
    setChartData(null);
    setChartError(null);
  }, []);

  return {
    chartData,
    chartLoading,
    chartError,
    fetchChartData,
    clearChartData,
  };
};
