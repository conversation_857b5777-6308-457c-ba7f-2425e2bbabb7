import { useCallback, useMemo,useState } from 'react';

import type { FilterConfig } from '@/types/table';

const INITIAL_FILTER_CONFIG: FilterConfig = {
  symbolSearch: '',
  usdSignal: 'all',
  btcSignal: 'all',
};

export const useFiltering = (initialConfig: FilterConfig = INITIAL_FILTER_CONFIG) => {
  const [filterConfig, setFilterConfig] = useState<FilterConfig>(initialConfig);

  const updateSymbolSearch = useCallback((search: string) => {
    setFilterConfig((prev) => ({ ...prev, symbolSearch: search }));
  }, []);

  const updateUsdSignal = useCallback((signal: FilterConfig['usdSignal']) => {
    setFilterConfig((prev) => ({ ...prev, usdSignal: signal }));
  }, []);

  const updateBtcSignal = useCallback((signal: FilterConfig['btcSignal']) => {
    setFilterConfig((prev) => ({ ...prev, btcSignal: signal }));
  }, []);



  const clearFilters = useCallback(() => {
    setFilterConfig(INITIAL_FILTER_CONFIG);
  }, []);

  const hasActiveFilters = useMemo(() => {
    return (
      filterConfig.symbolSearch.trim() !== '' ||
      filterConfig.usdSignal !== 'all' ||
      filterConfig.btcSignal !== 'all'
    );
  }, [filterConfig]);

  return {
    filterConfig,
    updateSymbolSearch,
    updateUsdSignal,
    updateBtcSignal,
    clearFilters,
    hasActiveFilters,
  };
};
