import { useEffect, useState } from 'react';

import { RESPONSIVE_CONSTANTS } from '@/constants/responsive';

export type LayoutType = 'mobile' | 'tablet' | 'desktop';

interface ResponsiveConfig {
  mobile: number;
  tablet: number;
  desktop: number;
}

const DEFAULT_BREAKPOINTS: ResponsiveConfig = {
  mobile: 768,
  tablet: 1024,
  desktop: RESPONSIVE_CONSTANTS.DESKTOP_MIN_WIDTH,
};

// Helper function to determine layout type based on width
const getLayoutTypeFromWidth = (width: number, breakpoints: ResponsiveConfig): LayoutType => {
  if (width < breakpoints.mobile) {return 'mobile';}
  if (width < breakpoints.tablet) {return 'tablet';}
  return 'desktop';
};

// Helper function to get initial layout type
const getInitialLayoutType = (breakpoints: ResponsiveConfig): LayoutType => {
  if (typeof window === 'undefined') {return 'desktop';}
  return getLayoutTypeFromWidth(window.innerWidth, breakpoints);
};

// Helper function to get initial window size
const getInitialWindowSize = () => ({
  width: typeof window === 'undefined' ? RESPONSIVE_CONSTANTS.DESKTOP_MIN_WIDTH : window.innerWidth,
  height: typeof window === 'undefined' ? RESPONSIVE_CONSTANTS.TABLET_MIN_WIDTH : window.innerHeight,
});

export const useResponsiveLayout = (breakpoints: ResponsiveConfig = DEFAULT_BREAKPOINTS) => {
  const [layoutType, setLayoutType] = useState<LayoutType>(() => getInitialLayoutType(breakpoints));
  const [windowSize, setWindowSize] = useState(getInitialWindowSize);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      setWindowSize({ width, height });
      setLayoutType(getLayoutTypeFromWidth(width, breakpoints));
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => { window.removeEventListener('resize', handleResize); };
  }, [breakpoints]);

  const isMobile = layoutType === 'mobile';
  const isTablet = layoutType === 'tablet';
  const isDesktop = layoutType === 'desktop';
  const isMobileOrTablet = isMobile || isTablet;

  return {
    layoutType,
    windowSize,
    isMobile,
    isTablet,
    isDesktop,
    isMobileOrTablet,
    breakpoints,
  };
};
