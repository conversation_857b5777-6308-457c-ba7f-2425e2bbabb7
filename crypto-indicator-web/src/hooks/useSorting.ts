import { useCallback,useState } from 'react';

import type { SortColumn, SortConfig, SortDirection } from '@/types/table';

const INITIAL_SORT_CONFIG: SortConfig = {
  column: null,
  direction: null,
};

export const useSorting = (initialConfig: SortConfig = INITIAL_SORT_CONFIG) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>(initialConfig);

  const handleSort = useCallback((column: SortColumn) => {
    setSortConfig((prevConfig) => {
      // If clicking the same column, cycle through: asc -> desc -> null
      if (prevConfig.column === column) {
        if (prevConfig.direction === 'asc') {
          return { column, direction: 'desc' };
        } else if (prevConfig.direction === 'desc') {
          return { column: null, direction: null };
        }
      }
      
      // If clicking a different column or no current sort, start with asc
      return { column, direction: 'asc' };
    });
  }, []);

  const clearSort = useCallback(() => {
    setSortConfig(INITIAL_SORT_CONFIG);
  }, []);

  const getSortDirection = useCallback((column: SortColumn): SortDirection => {
    return sortConfig.column === column ? sortConfig.direction : null;
  }, [sortConfig]);

  return {
    sortConfig,
    handleSort,
    clearSort,
    getSortDirection,
  };
};
