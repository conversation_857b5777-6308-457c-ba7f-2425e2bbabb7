import { useCallback, useState } from "react";

import type { AssetType } from "@/types/tabs";

interface UseTabNavigationReturn {
  activeTab: AssetType;
  setActiveTab: (tab: AssetType) => void;
  isActiveTab: (tab: AssetType) => boolean;
}

export const useTabNavigation = (defaultTab: AssetType = 'crypto'): UseTabNavigationReturn => {
  const [activeTab, setActiveTabState] = useState<AssetType>(defaultTab);

  const setActiveTab = useCallback((tab: AssetType): void => {
    setActiveTabState(tab);
  }, []);

  const isActiveTab = useCallback((tab: AssetType): boolean => {
    return activeTab === tab;
  }, [activeTab]);

  return {
    activeTab,
    setActiveTab,
    isActiveTab,
  };
};
