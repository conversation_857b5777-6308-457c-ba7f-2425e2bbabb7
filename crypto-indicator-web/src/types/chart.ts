import type { CryptoCurrencyStatisticsDto, StockStatisticsDto } from "@/generated";

export interface ChartData {
  time: string;
  value: number;
}

export interface CandlestickData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
}

export type ChartDataSource = CryptoCurrencyStatisticsDto | StockStatisticsDto;

export interface ChartModalProps {
  data: ChartDataSource;
  onClose: () => void;
}

export interface ChartContainerProps {
  data: ChartDataSource;
}
