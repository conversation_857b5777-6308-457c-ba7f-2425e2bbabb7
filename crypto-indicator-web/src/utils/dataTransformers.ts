import type { IndicatorValueDto } from '@/generated';
import type { CandlestickData,ChartData } from '@/types/chart';

export const dataTransformers = {
  /**
   * Transform indicator values to candlestick data format
   */
  transformCandlestickData: (indicatorValues: IndicatorValueDto[]): CandlestickData[] => {
    return indicatorValues
      .filter(item => item.open && item.high && item.low && item.close && item.timestamp)
      .map(item => ({
        time: item.timestamp.split('T')[0] as string,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },

  /**
   * Transform indicator values to SMMA line data format
   */
  transformSMMAData: (indicatorValues: IndicatorValueDto[], period: number): ChartData[] => {
    const fieldName = `smma_${period}` as keyof IndicatorValueDto;

    return indicatorValues
      .filter(item => item[fieldName] !== undefined && item[fieldName] !== null && item.timestamp)
      .map(item => ({
        time: item.timestamp.split('T')[0] as string,
        value: item[fieldName] as number,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },

  /**
   * Generic data transformer for any numeric field
   */
  transformIndicatorData: (
    indicatorValues: IndicatorValueDto[],
    field: keyof IndicatorValueDto
  ): ChartData[] => {
    return indicatorValues
      .filter(item => item[field] !== undefined && item[field] !== null && item.timestamp)
      .map(item => ({
        time: item.timestamp.split('T')[0] as string,
        value: item[field] as number,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },
};
