import { compareValues, getSignalPriority } from '@/utils/tableSorting';

import type { IndicatorValueDto, StockStatisticsDto } from '@/generated';
import type { StockSortColumn, StockSortConfig } from '@/types/table';

/**
 * Get sort value for a stock based on column
 */
const getSortValue = (
  stock: StockStatisticsDto,
  btcData: IndicatorValueDto | undefined,
  column: StockSortColumn,
): string | number => {
  const fiatData = stock.indicatorValues?.[0];

  switch (column) {
    case 'symbol': {
      return stock.symbol ?? '';
    }
    case 'usdPrice': {
      return fiatData?.close ?? 0;
    }
    case 'marketCap': {
      return fiatData?.volume ?? 0;
    }
    case 'usdSignal': {
      return getSignalPriority(fiatData?.color);
    }
    case 'btcPrice': {
      return btcData?.close ?? 0;
    }
    case 'btcSignal': {
      return getSignalPriority(btcData?.color);
    }
    default: {
      return '';
    }
  }
};

/**
 * Apply sorting to stock data
 */
export const applyStockSorting = (
  data: StockStatisticsDto[],
  btcStatistics: StockStatisticsDto[],
  sortConfig: StockSortConfig,
  findBtcDataForSymbol: (
    btcStats: StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined,
): StockStatisticsDto[] => {
  if (!sortConfig.column || !sortConfig.direction) {
    return data;
  }

  return [...data].sort((a, b) => {
    const btcDataA = findBtcDataForSymbol(btcStatistics, a.symbol);
    const btcDataB = findBtcDataForSymbol(btcStatistics, b.symbol);

    const { column } = sortConfig;
    if (column === null || column === undefined) {
      return 0;
    }

    const valueA = getSortValue(a, btcDataA, column);
    const valueB = getSortValue(b, btcDataB, column);

    const { direction } = sortConfig;
    const primaryResult = compareValues(valueA, valueB, direction);

    // Secondary sort by symbol for stable sorting
    if (primaryResult === 0) {
      return a.symbol.localeCompare(b.symbol);
    }

    return primaryResult;
  });
};
